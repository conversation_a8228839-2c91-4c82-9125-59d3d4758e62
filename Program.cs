using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Configuration;
using System;
using System.Net.Http;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace SignalRClient
{
    class Program
    {
        static async Task Main(string[] args)
        {
            CancellationTokenSource cts = new CancellationTokenSource();

            try
            {
                // 讀取配置文件
                IConfiguration configuration = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json")
                .Build();

                string hubUrl = configuration["SignalRConfig:HubUrl"];
                string areaCode = configuration["SignalRConfig:AreaCode"];
                string subscriptionType = configuration["SignalRConfig:SubscriptionType"];
                string username = configuration["AuthConfig:Username"];
                string password = configuration["AuthConfig:Password"];
                string loginUrl = configuration["AuthConfig:LoginUrl"];

                // 配置 HTTP 客戶端，處理 TLS 憑證（僅用於測試自簽憑證）
                var httpClientHandler = new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback = (message, cert, chain, errors) =>
                    {
                        // 僅在本地測試時忽略自簽憑證，生產環境應移除
                        return true;
                    }
                };
                var httpClient = new HttpClient(httpClientHandler);

                // 進行登入並獲取 token
                var loginRequest = new
                {
                    UserAccount = username,
                    UserPassword = password,
                    CampusCode = areaCode
                };
                var content = new StringContent(JsonSerializer.Serialize(loginRequest), Encoding.UTF8, "application/json");
                var response = await httpClient.PostAsync(loginUrl, content);

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Login failed: {await response.Content.ReadAsStringAsync()}");
                    return;
                }

                var responseData = JsonSerializer.Deserialize<LoginResponse>(await response.Content.ReadAsStringAsync());
                string token = responseData.Token;
                string userCampusCode = responseData.CampusCode;

                Console.WriteLine("Login successful. Token acquired.");

                // 配置 SignalR 連線，使用 JWT token 和院區代碼
                var hubConnection = new HubConnectionBuilder()
                    .WithUrl($"{hubUrl}?AreaCode={areaCode}&id={token}", options =>
                    {
                        // 配置 WebSocket 的 TLS 憑證驗證（僅用於測試）
                        options.HttpMessageHandlerFactory = (message) =>
                        {
                            if (message is HttpClientHandler clientHandler)
                            {
                                clientHandler.ServerCertificateCustomValidationCallback =
                                    (msg, cert, chain, errors) => true; // 忽略自簽憑證（僅限測試）
                            }
                            return message;
                        };
                    })
                    .WithAutomaticReconnect()
                    .Build();

                // 定義事件處理
                // if (subscriptionType == "1" || subscriptionType == "2")
                // {
                hubConnection.On<object>("taskOccurred", async (message) =>
                {
                    var task = TransformSignalRTask(message);
                    Console.WriteLine($"Received taskOccurred message:\n{JsonSerializer.Serialize(task, new JsonSerializerOptions { WriteIndented = true })}");
                });
                // }

                // if (subscriptionType == "1" || subscriptionType == "3")
                // {
                hubConnection.On<object>("position", async (message) =>
                {
                    var position = TransformSignalRPosition(message);
                    Console.WriteLine($"Received position message:\n{JsonSerializer.Serialize(position, new JsonSerializerOptions { WriteIndented = true })}");
                });
                // }

                // 處理連線錯誤
                hubConnection.On<string>("Error", (error) =>
                {
                    Console.WriteLine($"Error: {error}");
                });

                // 連線狀態變更處理
                hubConnection.Closed += async (error) =>
                {
                    Console.WriteLine($"Connection closed: {error?.Message}");
                    await Task.Delay(5000);
                    await hubConnection.StartAsync();
                };

                hubConnection.Reconnecting += (error) =>
                {
                    Console.WriteLine($"Connection lost: {error?.Message}. Reconnecting...");
                    return Task.CompletedTask;
                };

                hubConnection.Reconnected += (connectionId) =>
                {
                    Console.WriteLine($"Connection reestablished with ID: {connectionId}");
                    return Task.CompletedTask;
                };

                // 啟動連線
                try
                {
                    await hubConnection.StartAsync();
                    Console.WriteLine("Connected to SignalR Hub (WSS).");

                    // 訂閱 topic
                    // string topicPrefix = $"{userCampusCode}_";
                    // string taskTopic = $"{topicPrefix}tasks";
                    // string positionTopic = $"{topicPrefix}positions";

                    // if (subscriptionType == "1" || subscriptionType == "2")
                    // {
                    //     await hubConnection.InvokeAsync("SubscribeToTopic", taskTopic);
                    //     Console.WriteLine($"Subscribed to topic: {taskTopic}");
                    // }

                    // if (subscriptionType == "1" || subscriptionType == "3")
                    // {
                    // await hubConnection.InvokeAsync("SubscribeToTopic", positionTopic);
                    // Console.WriteLine($"Subscribed to topic: {positionTopic}");
                    // }

                    // 模擬發送測試訊息（可選）
                    // Console.WriteLine("Enter a message to send to the task topic (or press Enter to skip):");
                    // string message = Console.ReadLine();
                    // if (!string.IsNullOrEmpty(message) && (subscriptionType == "1" || subscriptionType == "2"))
                    // {
                    //     await hubConnection.InvokeAsync("SendMessageToTopic", taskTopic, message);
                    // }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error while starting connection: {ex.Message}");
                    return;
                }

                // 保持程式運行，直到取消
                Console.WriteLine("Press Ctrl+C to disconnect and exit...");
                await Task.Delay(Timeout.Infinite, cts.Token);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
                if (ex.InnerException != null)
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
            finally
            {
                // 斷開連線
                await hubConnection.StopAsync();
                Console.WriteLine("SignalR Disconnected.");
            }
        }

        // 轉換 taskOccurred 訊息結構
        private static object TransformSignalRTask(object task)
        {
            var item = JsonSerializer.Deserialize<JsonElement>(JsonSerializer.Serialize(task));
            return new
            {
                taskId = item.TryGetProperty("taskId", out var taskId) ? taskId.GetString() : null,
                serviceCode = item.TryGetProperty("serviceCode", out var serviceCode) ? serviceCode.GetString() : null,
                action = item.TryGetProperty("action", out var action) ? action.GetString() : null,
                deptCode = item.TryGetProperty("deptCode", out var deptCode) ? deptCode.GetString() : null,
                deptName = item.TryGetProperty("deptName", out var deptName) ? deptName.GetString() : null,
                objCode = item.TryGetProperty("objectCode", out var objCode) ? objCode.GetString() : null,
                objName = item.TryGetProperty("objectName", out var objName) ? objName.GetString() : null,
                objType = item.TryGetProperty("objectType", out var objType) ? objType.GetString() : null,
                groupCode = item.TryGetProperty("groupCode", out var groupCode) ? groupCode.GetString() : null,
                pid = item.TryGetProperty("pid", out var pid) ? pid.GetString() : null,
                startsAt = item.TryGetProperty("startsAt", out var startsAt) ? startsAt.GetString() : null,
                finishesAt = item.TryGetProperty("finishesAt", out var finishesAt) && finishesAt.ValueKind != JsonValueKind.Null ? finishesAt.GetString() : null,
                plane = item.TryGetProperty("plane", out var plane) ? new
                {
                    planeCode = plane.TryGetProperty("planeCode", out var planeCode) ? planeCode.GetString() : null,
                    customPlaneCode = plane.TryGetProperty("customPlaneCode", out var customPlaneCode) ? customPlaneCode.GetString() : null,
                    planeName = plane.TryGetProperty("planeName", out var planeName) ? planeName.GetString() : null,
                    planeMapPath = plane.TryGetProperty("planeMapPath", out var planeMapPath) ? planeMapPath.GetString() : null
                } : null,
                station = item.TryGetProperty("station", out var station) ? new
                {
                    sid = station.TryGetProperty("sid", out var sid) ? sid.GetString() : null,
                    stationName = station.TryGetProperty("stationName", out var stationName) ? stationName.GetString() : null
                } : null,
                locationCode = item.TryGetProperty("locationCode", out var locationCode) ? locationCode.GetString() : null,
                locationName = item.TryGetProperty("locationName", out var locationName) ? locationName.GetString() : null
            };
        }

        // 轉換 position 訊息結構
        private static object TransformSignalRPosition(object position)
        {
            var item = JsonSerializer.Deserialize<JsonElement>(JsonSerializer.Serialize(position));
            return new
            {
                objectInfo = item.TryGetProperty("object", out var obj) ? new
                {
                    objCode = obj.TryGetProperty("objectCode", out var objCode) ? objCode.GetString() : null,
                    name = obj.TryGetProperty("name", out var name) ? name.GetString() : null,
                    groupCode = obj.TryGetProperty("groupCode", out var groupCode) ? groupCode.GetString() : null,
                    objType = obj.TryGetProperty("objectType", out var objType) ? objType.GetString() : null,
                    pid = obj.TryGetProperty("pid", out var pid) ? pid.GetString() : null,
                    usageDepartCode = obj.TryGetProperty("usageDepartCode", out var usageDepartCode) ? usageDepartCode.GetString() : null
                } : null,
                plane = item.TryGetProperty("plane", out var plane) ? new
                {
                    planeCode = plane.TryGetProperty("planeCode", out var planeCode) ? planeCode.GetString() : null,
                    customPlaneCode = plane.TryGetProperty("customPlaneCode", out var customPlaneCode) ? customPlaneCode.GetString() : null,
                    planeMapPath = plane.TryGetProperty("planeMapPath", out var planeMapPath) ? planeMapPath.GetString() : null,
                    planeName = plane.TryGetProperty("planeName", out var planeName) ? planeName.GetString() : null
                } : null,
                station = item.TryGetProperty("station", out var station) ? new
                {
                    sid = station.TryGetProperty("sid", out var sid) ? sid.GetString() : null,
                    locCode = station.TryGetProperty("locCode", out var locCode) ? locCode.GetString() : null,
                    locName = station.TryGetProperty("locName", out var locName) ? locName.GetString() : null,
                    diffPositionX = station.TryGetProperty("diffPositionX", out var diffX) ? diffX.GetDouble() : 0.0,
                    diffPositionY = station.TryGetProperty("diffPositionY", out var diffY) ? diffY.GetDouble() : 0.0
                } : null,
                latestPositionTime = item.TryGetProperty("latestPositionTime", out var latestPositionTime) ? latestPositionTime.GetString() : null
            };
        }

        private class LoginResponse
        {
            public string Token { get; set; }
            public string CampusCode { get; set; }
        }
    }
}